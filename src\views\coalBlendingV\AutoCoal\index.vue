<template>
  <div class="app-layout">
    <div v-loading="isLoading"
         :element-loading-text="loadingText"
         element-loading-background="rgba(255, 255, 255,0.9)"
         element-loading-custom-class="fixed-center-loading"
         class="auto">
      <el-card class="card">
        <div class="steps">
          <div class="step-out">
            <div class="checkBg">1</div>
            <div class="text">添加煤种</div>
            <div class="line"></div>
          </div>
          <div class="step-out">
            <div class="checkBg">2</div>
            <div class="text">指标录入</div>
            <div :class="active >= 3 ? 'line' : 'gray-line'"></div>
          </div>
          <div class="step-out">
            <div v-show="active >= 3">
              <div class="checkBg">3</div>
            </div>
            <div v-show="active < 3" class="gray-step">3</div>
            <div class="text">计算操作</div>
            <div :class="active >= 4 ? 'line' : 'gray-line'"></div>
          </div>
          <div class="step-out">
            <div v-show="active >= 4">
              <div class="checkBg">4</div>
            </div>
            <div v-show="active < 4" class="gray-step">4</div>
            <div class="text">配煤结果</div>
          </div>
        </div>
      </el-card>
      <el-card class="card">
        <div slot="header">
          <div style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            ">
            <div class="card-title">添加煤种</div>
            <el-button v-if="perms[`${curd}:add`] || false" type="primary" @click="add">
              添加煤种
            </el-button>
          </div>
        </div>
        <div v-if="space.workspaceCoalRockList.length > 0" class="m5">
          <el-table :data="space.workspaceCoalRockList" :header-cell-style="activeHeader" :row-style="activeRow"
                    :summary-method="getSummary" border class="coal-table" show-summary>
            <el-table-column width="55">
              <template slot-scope="scope">
                <div class="tc">{{ scope.$index + 1 }}</div>
              </template>
            </el-table-column>
            <el-table-column label="名称" prop="name" width="90">
              <template slot-scope="scope">
                <!-- <el-badge :value="12" class="item"> -->
                  <!-- @click="edit(scope.row)"   @click="editV(scope.row)"   @click="edit(scope.row)"-->
                <div> 
                  <span v-if="scope.row.source === COAL_SOURCE_TYPE.WAI_BU || scope.row.source === 'wb'" class="under-line"
                        >{{ scope.row.name }}</span>
                  <span v-else-if="scope.row.source === COAL_SOURCE_TYPE.ZI_YOU || scope.row.source === 'zy'" class="under-line"
                        >{{ scope.row.name }}</span>
                  <span v-else-if="scope.row.source === COAL_SOURCE_TYPE.ZSMJ || scope.row.source === 'zsmj'" class="under-line"
                        >{{ scope.row.name }}</span>
                  <span v-else class="under-line" @click="edit(scope.row)">{{ scope.row.name }}</span>

                  <!-- 掌上煤焦标签，最高优先级 -->
                  <div v-if="scope.row.dataType === 'zsmy' || scope.row.source === COAL_SOURCE_TYPE.ZSMJ || scope.row.coalSource === 'ZSMJ' || scope.row.coalSource === 'zsmj'" class="subscript" style="
                      border-top: solid 15px #f9982e;
                      border-right: solid 15px #f9982e;
                    ">
                    <span>掌</span>
                  </div>
                  <!-- 外部煤源标签 -->
                  <div v-else-if="scope.row.dataType === 'wb' || scope.row.source === COAL_SOURCE_TYPE.WAI_BU" class="subscript" style="
                      border-top: solid 15px #00ccff;
                      border-right: solid 15px #00ccff;
                      ">
                    <span>外</span>
                  </div>
                  <!-- 自有煤源标签 -->
                  <div v-else-if="scope.row.dataType === 'zy' || scope.row.source === COAL_SOURCE_TYPE.ZI_YOU" class="subscript" style="
                      border-top: solid 15px red;
                      border-right: solid 15px red;
                    ">
                    <span>自</span>
                  </div>
                </div>
                <!-- </el-badge> -->
              </template>
            </el-table-column>
            <el-table-column label="煤种" min-width="50" prop="type">
              <template slot-scope="scope">
                <div v-if="scope.row.isCoalRock === 'Y'" class="subscript" style="
                    border-top: solid 15px #f9982e;
                    border-right: solid 15px #f9982e;
                  ">
                  <span>岩</span>
                </div>

                <span>{{ scope.row.type }}</span>
              </template>
            </el-table-column>
            <el-table-column label="人工配比">
              <el-table-column label="数量" prop="quantity" width="60">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.quantity" class="yellow-input" min="0" placeholder="数量" type="number"
                            @focus="onFcous" @input="loseFcous(scope.$index, scope.row, scope.column)"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="占比(%)" prop="percent2" width="60">
                <template slot-scope="scope">
                  <span style="color: #f8a20f">{{ scope.row.percent2 }}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="指定配比%" width="110">
              <template slot-scope="scope">
                <div style="display: flex; justify-content: space-evenly">
                  <el-select v-model="scope.row.percentCond" class="yellow-input" placeholder="空" style="width: 50px">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                  <el-input v-model="scope.row.percent1" class="yellow-input" min="0" placeholder="配比" style="width: 50px"
                            type="number"></el-input>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="R0(%)" min-width="50" prop="macR0">
              <template slot-scope="scope">
                <el-input v-model="scope.row.macR0" placeholder="R0" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="标准差S" min-width="50" prop="macS">
              <template slot-scope="scope">
                <el-input v-model="scope.row.macS" placeholder="输入数值" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="Ad(%)" min-width="50" prop="cleanAd">
              <template slot-scope="scope">
                <el-input v-model="scope.row.cleanAd" placeholder="Ad" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="Vdaf(%)" min-width="50" prop="cleanVdaf">
              <template slot-scope="scope">
                <el-input v-model="scope.row.cleanVdaf" placeholder="Vdaf" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="St,d(%)" min-width="50" prop="cleanStd">
              <template slot-scope="scope">
                <el-input v-model="scope.row.cleanStd" placeholder="St,d" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="G" min-width="45" prop="procG">
              <template slot-scope="scope">
                <el-input v-model="scope.row.procG" placeholder="G" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="Y(mm)" min-width="45" prop="procY">
              <template slot-scope="scope">
                <el-input v-model="scope.row.procY" placeholder="Y" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="含税煤价">
              <el-table-column label="元/吨" min-width="60">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.factoryPrice" placeholder="含税煤价（元/吨）" type="number"></el-input>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="运费">
              <el-table-column label="元/吨" min-width="60">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.activePriceTransportPriceNoTax" placeholder="运费（元/吨）" type="number"></el-input>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="配煤成本">
              <el-table-column label="元/吨" min-width="60">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.arrivePrice" placeholder="配煤成本（元/吨）" type="number"></el-input>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="X(mm)" min-width="45" prop="procX">
              <template slot-scope="scope">
                <el-input v-model="scope.row.procX" placeholder="X" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="水分" min-width="45" prop="cleanMt">
              <template slot-scope="scope">
                <el-input v-model="scope.row.cleanMt" placeholder="水分" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="CSR" min-width="45" prop="qualCsr">
              <template slot-scope="scope">
                <el-input v-model="scope.row.qualCsr" placeholder="CSR" type="number"></el-input>
              </template>
            </el-table-column>
                
            <el-table-column align="center" label="操作" width="120" >
              <template slot-scope="scope" >
                <span v-if="(perms[`${curd}:add`] || false) && scope.row.dataType !== 'nb'">
                  <span style="
                      border-radius: 5px;
                      background: #33cad9;
                      color: #fff;
                      padding: 4px 8px;
                      margin: 0 px;
                    " @click="handleSaveCoal(scope.row)">保存</span>
                </span>
                <span v-if="perms[`${curd}:delete`] || false">
                  <span style="
                      border-radius: 5px;
                      background: #2f79e8;
                      color: #fff;
                      padding: 4px 8px;
                      margin: 0 5px;
                    " @click="remove(scope.row)">删除</span>
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else>
          <el-empty :image="empty" description="请先添加煤种数据"></el-empty>
        </div>
      </el-card>
      <el-form :model="limit">
        <el-card class="card">
          <div slot="header">
            <div style="display: flex; align-items: center">
              <div class="card-title">指标录入</div>
              <div class="" style="
                  width: 550px;
                  padding: 0 5px;
                  display: flex;
                  align-items: center;
                  line-height: 30px;
                ">
                <!--                                <div class="label">目标值：</div>-->
                <div class="" style="margin-left: 20px">
                  <SnApiSelect ref="coalWashingTarget" v-model="targetValue" api-name="listCoalWashingTargetValueConfig"
                               placeholder="请选择目标值" @select="handleTargetValueChange" />
                </div>

                <el-button class="right-btn mr" style="margin-left: 20px" type="primary">
                  <div style="display: flex; align-items: center" @click="openPlanManageInfo">
                    保存
                  </div>
                </el-button>
              </div>
            </div>
          </div>

          <div style="display: flex; margin-top: -10px; position: relative">
            <div class="container">
              <div>
                <p class="single" style="width: 70px"></p>
                <div class="single">
                  <span class="title-text">灰分Ad</span>
                </div>
                <div class="single">
                  <span class="title-text">硫St,d</span>
                </div>
                <div class="single">
                  <span class="title-text">Vdaf</span>
                </div>
                <div class="single">
                  <span class="title-text">粘结G</span>
                </div>
                <div class="single">
                  <span class="title-text">胶质Y</span>
                </div>
                <div class="single">
                  <span class="title-text">CSR</span>
                </div>
              </div>
              <div style="margin: 5px 0">
                <p class="single single-wrap" style="width: 70px">上限:</p>
                <div class="single">
                  <input v-model="limit.cInAdH" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInStdH" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInVdafH" type="number" />
                </div>
              </div>
              <div style="margin: 5px 0">
                <p class="single single-wrap" style="width: 70px">质量目标:</p>
                <div class="single">
                  <input v-model="limit.cInAd" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInStd" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInVdaf" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInG" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInY" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cJtCsrL" type="number" />
                </div>
              </div>
              <div style="height: 28px">
                <p class="single single-wrap" style="width: 70px">下限:</p>
                <div class="single">
                  <input v-model="limit.cInAdL" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInStdL" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInVdafL" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInGL" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cInYL" type="number" />
                </div>
                <div class="single">
                  <input v-model="limit.cJtCsrH" type="number" />
                </div>
              </div>
            </div>
            <div class="card-wrap">
              <div class="line-box"></div>
            </div>
            <div class="container" style="width: 200px; display: flex; margin-top: 30px">
              <div class="single">
                <!--          <el-checkbox v-model="checked">限制标准差</el-checkbox>-->
                <span style="width: 60px">标准差≤</span>
                <input v-model="limit.cInSH" style="width: 60px" type="number" />
                <span style="color: red">注意:所有煤种都需要有反射率数据</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-form>
      <el-form ref="spaceForm" :inline="true" :model="space" :rules="spaceRules" class="mb0 container">
        <el-card class="card" style="min-height: 124px">
          <div style="
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
            ">
            <div>
              <div class="card-title">计算操作</div>
            </div>
            <div>
              <el-button v-if="perms[`${curd}:artificialCount`] || false" class="right-btn mr" type="primary"
                         @click="calculate('spaceForm', 'OptTypeManual')">
                <div style="display: flex; align-items: center">人工计算</div>
              </el-button>
              <el-button v-if="perms[`${curd}:intelligenceCount`] || false" class="right-btn mr" type="primary"
                         @click="calculate('spaceForm', 'OptTypeSpecify')">
                智能计算
              </el-button>
              <el-button v-if="perms[`${curd}:empty`] || false" class="right-btn" type="primary" @click="clearList">
                清 空
              </el-button>
            </div>
          </div>
          <div style="display: flex; justify-content: flex-end; margin-top: 5px">
            <el-tag type="danger"><i class="el-icon-info"></i>如对自动计算存疑，请指定某些煤比例再点击指定提供方案
            </el-tag>
          </div>
        </el-card>
        <el-card v-if="active >= 4" class="card">
          <div slot="header">
            <div style="
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
              <div class="card-title">
                配煤结果
                <el-tag type="danger"><i class="el-icon-info"></i>
                  推荐方案是大数据计算出来的不限制灰硫下限的成本最低方案
                </el-tag>
              </div>
            </div>
          </div>
          <div class="m5 table-content">
            <el-table :border="false" :data="space.workspaceCoalWashingResultList" :header-cell-style="activeHeader"
                      :row-style="activeRow" :summary-method="showDiff" highlight-current-row show-summary
                      @row-click="handleRowClick">
              <el-table-column align="center" label="配煤比例(%)">
                <el-table-column label="方案" min-width="80" prop="projectName">
                  <template slot-scope="scope">
                    <span :class="
                        scope.row.projectName === '推荐方案'
                          ? 'redColor'
                          : 'black'
                      ">{{ scope.row.projectName }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-for="item in getWorkspaceCoalRockList" :key="item.id" :label="`${item.name}`" :prop="item.id"
                                 min-width="80">
                </el-table-column>
              </el-table-column>
              <el-table-column align="center" label="配合煤指标">
                <el-table-column label="Ad(%)" min-width="50" prop="inAd">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.inAd ? scope.row.inAd.toFixed(2) : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Vdaf(%)" min-width="60" prop="inVdaf">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.inVdaf ? scope.row.inVdaf.toFixed(2) : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="St,d(%)" min-width="60" prop="inStd">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.inStd ? scope.row.inStd.toFixed(2) : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="G" min-width="50" prop="inG">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.inG ? scope.row.inG.toFixed(2) : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="Y(mm)" min-width="60" prop="inY">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.inY ? scope.row.inY.toFixed(2) : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="X(mm)" min-width="60" prop="inX">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.inX ? scope.row.inX.toFixed(2) : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="R0(%)" min-width="60" prop="theoryMacR0">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.theoryMacR0
                        ? scope.row.theoryMacR0.toFixed(3)
                        : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="水分" min-width="60" prop="inMt">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.inMt ? scope.row.inMt.toFixed(2) : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="CSR" min-width="60" prop="qualCsr">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.qualCsr ? scope.row.qualCsr.toFixed(2) : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="标准差" prop="theoryMacS" width="70">
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.theoryMacS
                        ? scope.row.theoryMacS.toFixed(3)
                        : ""
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="价格(元/吨)" min-width="70" prop="inPrice"></el-table-column>
              </el-table-column>

              <el-table-column align="center" fixed="right" label="操作" prop="inPrice" width="100">
                <template slot-scope="scope">
                  <el-button v-if="perms[`${curd}:saveprogramme`] || false" :loading="isLoading" type="reset_production"
                             @click="save(scope.$index)">
                    保存方案
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- 铲数和吨数信息 -->
          <el-table v-if="space.shovelTonnageNum" :data="space.shovelTonnageNum" border style="width: 100%; margin-top: 15px"
                    max-height="250">
            <el-table-column :label="space.shovelTonnageNum[0].projectName ? space.shovelTonnageNum[0].projectName : ''"
                             max-width="90">
              <template slot-scope="scope">
                <span>{{
                      scope.row.name ? scope.row.name : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column label="总数" max-width="90" prop="projectName">
              <template slot-scope="scope">
                <el-input v-if="scope.$index === 0 " @change="sendCoalPercentData(scope.row)"
                          v-model="scope.row.totalShovelsNumber" align="center" placeholder="输入数值" type="number"></el-input>
                <el-input v-if="scope.$index === 1 " @change="sendCoalPercentData(scope.row)" v-model="scope.row.totalTons"
                          align="center" placeholder="输入数值" type="number"></el-input>
              </template>
            </el-table-column>
            <!-- 动态获取品名 -->
            <el-table-column v-for="item in getWorkspaceCoalRockList" :key="item.id" :label="`${item.name}`" :prop="item.name"
                             max-width="90">
              <template slot-scope="scope">
                <el-input @change="sendCoalPercentData(scope.row)" v-model="scope.row[item.name]" align="center"
                          placeholder="输入数值" type="number"></el-input>
              </template>
            </el-table-column>

            <el-table-column style="background-color: #333" label="Ad(%)" min-width="83" prop="inAd">
              <template slot-scope="scope">
                <span>{{
                      scope.row.inAd ? scope.row.inAd.toFixed(2) : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="Vdaf(%)" min-width="83" prop="inVdaf">
              <template slot-scope="scope">
                <span>{{
                      scope.row.inVdaf ? scope.row.inVdaf.toFixed(2) : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="St,d(%)" min-width="83" prop="inStd">
              <template slot-scope="scope">
                <span>{{
                      scope.row.inStd ? scope.row.inStd.toFixed(2) : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="G" min-width="83" prop="inG">
              <template slot-scope="scope">
                <span>{{
                      scope.row.inG ? scope.row.inG.toFixed(2) : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="Y(mm)" min-width="83" prop="inY">
              <template slot-scope="scope">
                <span>{{
                      scope.row.inY ? scope.row.inY.toFixed(2) : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="X(mm)" min-width="82" prop="inX">
              <template slot-scope="scope">
                <span>{{
                      scope.row.inX ? scope.row.inX.toFixed(2) : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="R0(%)" min-width="82" prop="theoryMacR0">
              <template slot-scope="scope">
                <span>{{
                      scope.row.theoryMacR0
                        ? scope.row.theoryMacR0.toFixed(3)
                        : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="水分" min-width="82" prop="inMt">
              <template slot-scope="scope">
                <span>{{
                      scope.row.inMt ? scope.row.inMt.toFixed(2) : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="CSR" min-width="82" prop="qualCsr">
              <template slot-scope="scope">
                <span>{{
                      scope.row.qualCsr ? scope.row.qualCsr.toFixed(2) : ""
                    }}</span>
              </template>
            </el-table-column>
            <el-table-column style="background-color: #333" label="标准差" prop="theoryMacS" min-width="83">
              <template slot-scope="scope">
                <span>{{
                      scope.row.theoryMacS
                        ? scope.row.theoryMacS.toFixed(3)
                        : ""
                    }}</span>
              </template>
            </el-table-column>
          </el-table>
          <el-tag style="margin-top: 10px" type="danger">
            选中上面的方案，计算方案对应铲数，铲数默认数据是根据配比自动计算得出，支持修改
          </el-tag>
          <div v-if="impChartDataList.length > 0" class="m5" style="background: #fff; margin-top: 10px">
            <column-chart v-if="impChartDataList[0].xData.length > 0" :impChartData="impChartDataList[0]"></column-chart>
          </div>

          <div style="margin: 0 10%; background: #fff">
            <el-table v-if="leftData.length > 0" :data="leftData" :show-header="false" border>
              <el-table-column min-width="40" prop="brownCoal"></el-table-column>
              <el-table-column min-width="50" prop="longFlame"></el-table-column>
              <el-table-column min-width="60" prop="gasCoal"></el-table-column>
              <el-table-column min-width="60" prop="thirdCokingCoal"></el-table-column>
              <el-table-column min-width="60" prop="fatCoal"></el-table-column>
              <el-table-column min-width="60" prop="cokingCoal"></el-table-column>
              <el-table-column min-width="60" prop="leanCoal"></el-table-column>
              <el-table-column min-width="60" prop="meagerLeanCoal"></el-table-column>
              <el-table-column min-width="60" prop="meagerCoal"></el-table-column>
            </el-table>
          </div>

          <div v-if="impChartDataList.length > 0" class="m5" style="background: #fff; margin-top: 10px">
            <column-chart v-if="impChartDataList[1].xData.length > 0" :impChartData="impChartDataList[1]"></column-chart>
          </div>

          <div style="margin: 0 10%; background: #fff">
            <el-table v-if="rightData.length > 0" :data="rightData" :show-header="false" border>
              <el-table-column min-width="40" prop="brownCoal"></el-table-column>
              <el-table-column min-width="60" prop="longFlame"></el-table-column>
              <el-table-column min-width="60" prop="gasCoal"></el-table-column>
              <el-table-column min-width="60" prop="thirdCokingCoal"></el-table-column>
              <el-table-column min-width="60" prop="fatCoal"></el-table-column>
              <el-table-column min-width="60" prop="cokingCoal"></el-table-column>
              <el-table-column min-width="60" prop="leanCoal"></el-table-column>
              <el-table-column min-width="60" prop="meagerLeanCoal"></el-table-column>
              <el-table-column min-width="60" prop="meagerCoal"></el-table-column>
            </el-table>
          </div>

          <div v-if="active >= 4">
            <div class="res-title">反射率</div>
            <el-table ref="rateTable" :data="rateList" :show-header="false" border>
              <el-table-column v-for="(item, index) in coalMacRManualList" v-if="coalMacRManualList.length > 1" :key="index"
                               width="80">
                <template slot-scope="scope">{{
                  scope.row[item.rangeName]
                }}</template>
              </el-table-column>
              <el-table-column v-for="(item, index) in coalMacRSpecifyList"
                               v-if="coalMacRSpecifyList.length > 1 && rateList.length === 2" :key="item.rangeName" width="80">
                <template slot-scope="scope">{{
                  scope.row[item.rangeName]
                }}</template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
        <!-- <el-card v-if="active >= 4">
                  <div slot="header">
                    <div style="display: flex;justify-content: space-between; align-items: center">
                      <div class="card-title">反射率</div>
                    </div>
                  </div>
                  <div class="m5 table-content">
                    <el-table :show-header="false" :data="rateList" border ref="rateTable">
                      <el-table-column v-if="coalMacRManualList.length > 1" v-for="(item,index)  in coalMacRManualList" :key="index"
                                       width="80">
                        <template slot-scope="scope">{{scope.row[item.rangeName] }}</template>
                      </el-table-column>
                      <el-table-column v-if="coalMacRSpecifyList.length > 1 && rateList.length === 2"
                                       v-for="(item,index) in coalMacRSpecifyList" :key="item.rangeName" width="80">
                        <template slot-scope="scope">{{scope.row[item.rangeName] }}</template>
                      </el-table-column>
                    </el-table>

                  </div>
                </el-card> -->
      </el-form>
      <el-dialog :before-close="resetChecked" :close-on-click-modal="false" :close-on-press-escape="false" :modal="false"
                 :visible.sync="dialogFormVisible" fullscreen title="选择煤种">
        <coal ref="coal" canChoose class="p10" isCoalRock="Y" isShowCheckBox @choose="choose" @multChoose="addCoals"></coal>
      </el-dialog>
      <el-dialog :before-close="closeDialog" :close-on-click-modal="false" :close-on-press-escape="false" :modal="false"
                 :title="entityForm.name" :visible.sync="dialogCoalEntityFormVisible" fullscreen>
        <el-row class="app-container">
          <div class="fr">
            <el-button :loading="entityFormLoading" type="primary" @click="entityFormSave">关闭</el-button>
          </div>
        </el-row>
        <coal-entity-form ref="entityForm" :entityForm="entityForm"></coal-entity-form>
      </el-dialog>
      <el-dialog :before-close="closeProjectNameForm" :close-on-click-modal="false" :close-on-press-escape="false" :modal="false"
                 :visible.sync="dialogProjectVisible" class="saveSchemeDialog" title="请输入方案名称" top="5vh" width="500px">
        <el-form v-if="dialogProjectVisible" ref="projectNameForm" :model="projectForm" label-width="80px">
          <el-row>
            <el-col :span="20">
              <el-form-item :rules="[{ required: true, message: '请输入名称 ' }]" label="名称" prop="name">
                <el-input v-model="projectForm.name" placeholder="请输入内容"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeProjectNameForm">取消</el-button>
          <el-button :loading="isLoading" style="color: #fff" type="primary" @click="saveProject">提交
          </el-button>
        </div>
      </el-dialog>
    </div>
    <el-dialog :before-close="handleClose" :visible.sync="chooseVisible" class="addCoal" title="选择煤种" top="3vh" width="85%">
      <Coal ref="coal" :height="650" canChoose name="coal" @closeChoose="handleClose(true)"></Coal>
    </el-dialog>
    <AddCoal :add-visible="addVisible" :details="coalDetail" isSourceWash @closeVisible="handleClose(true, 'add')"></AddCoal>
    <addCoalzy :add-visible="addVisibleV" :details="coalDetailV" @closeVisible="handleClose(true, 'add')">
    </addCoalzy>
    <ChooseCoal v-if="chooseInfo.visitable" v-model="chooseInfo.visitable" :record="chooseInfo.record" @ok="getNewCoalRock" />
    <AddCoalConfig v-if="planManageInfo.visitable" v-model="planManageInfo.visitable" :record="planManageInfo.record"
                   @ok="reloadPlanList" />
  </div>
</template>
<script>
import {
  addCoalWashing,
  coalWashingQualityOptimizeByType,
  deleteWashingById,
  getNewCoalRock,
  getWashName,
  remakeNewCoal,
  removeWashAllCoal,
  saveWashProjectApi,
  syncCoal,
  saveData,
  getshovelTonnage,
  deleteProjectName,
  addToMyCoalSource
} from "@/api/coalWashing";
import Func from "@/utils/func";
import Mixins from "@/utils/mixins";
import ColumnChart from "@/components/Chart/columnChart";
import AddCoal from "@/components/Coal/addCoal";
import addCoalzy from "@/components/Coal/addCoalzy";
import empty from "@/assets/my/empty.png";
import { COAL_SOURCE_TYPE } from "@/const";
import ChooseCoal from "@/views/coalBlendingV/AutoCoal/components/ChooseCoal/index.vue";
import CalcUtils from "@/utils/calcUtils";
import { isDef } from "@/utils/is";

import AddCoalConfig from "@/views/coalBlendingV/AutoCoalConfig/components/AddCoalConfig.vue";

const limit = {
  cInAd: null,
  cInAdH: null,
  cInAdL: null,
  cInG: null,
  cInGL: null,
  cInSH: null,
  cInStd: null,
  cInStdH: null,
  cInStdL: null,
  cInVdaf: null,
  cInVdafH: null,
  cInVdafL: null,
  cInXL: null,
  cInY: null,
  cInYL: null,
  cJtCsrL: null,
  cJtCsrH: null,
};
const entityForm = {
  id: "",
  name: "",
  batchCode: "",
  type: "JM", // 默认焦煤
  province: "",
  factoryPrice: "",
  transitFee: "",
  roadCost: "",
  arrivePrice: "",
  arriveFactory: "JT",
  mineDepth: "",
  rawMt: "",
  rawAd: "",
  rawPointFive: "",
  rawOnePointFour: "",
  rawAdIn: "",
  rawStd: "",
  rawVdaf: "",
  rawG: "",
  cleanVdaf: "",
  cleanAd: "",
  cleanStd: "",
  cleanMt: "",
  cleanP: "",
  procG: "",
  procY: "",
  procX: "",
  procMf: "",
  procTp: "",
  procTmax: "",
  procTk: "",
  procCrc: "",
  procA: "",
  procB: "",
  macR0: "",
  macS: "",
  macV: "",
  macI: "",
  macE: "",
  comSiO2: "",
  comAl2O3: "",
  comFe2O3: "",
  comCaO: "",
  comMgO: "",
  comNa2O: "",
  comK2O: "",
  comTiO2: "",
  comP2O5: "",
  comSO3: "",
  cfeCp: "",
  cfeCe: "",
  qualScon: "",
  qualPcon: "",
  qualM40: "",
  qualM10: "",
  qualCsr: "",
  qualCri: "",
  qualTestCond: "",
  crGk: "",
  crBk: "",
  crTk: "",
  city: "",
  dataBelong: "",
  dataType: "",
  createBy: "",
  createDate: "",
  updateBy: "",
  updateDate: "",
  remarks: "",
  ext: "",
};
const components = {
  ColumnChart,
  AddCoal,
  addCoalzy,
};
export default {
  mixins: [Mixins],
  components: {
    ...components,
    ChooseCoal,
    AddCoalConfig,
  },
  data() {
    return {
      planManageInfo: {
        visitable: false,
        record: {},
      },
      initialCoalPercentContent: null,
      firstClickFlags: new Set(), // 记录每个方案是否是第一次点击
      COAL_SOURCE_TYPE,
      curd: "autoBlending",
      addVisible: false,
      coalDetail: {},
      coalDetailV: {},
      addVisibleV: false,
      chooseVisible: false,
      empty,
      active: 2,
      isAutoSuccess: "",
      isSuccess: "",
      isExit: true,
      isLoading: false,
      loadingText: "",
      dialogFormVisible: false,
      dialogProjectVisible: false,
      projectForm: {
        index: 0,
        name: "",
        projectType: "",
      },
      rateList: [],
      value: "",
      checked: "",
      options: [
        {
          value: "le",
          label: "≤",
        },
        {
          value: "ge",
          label: "≥",
        },
        {
          value: "eq",
          label: "=",
        },
      ],
      spaceRules: {
        bulkDensity: [
          { required: true, message: "请输入堆密度", trigger: "blur" },
        ],
        fineness: [{ required: true, message: "请输入细度", trigger: "blur" }],
        timeAjust: [
          { required: true, message: "请输入结焦时间", trigger: "blur" },
        ],
        tempeAjust: [
          { required: true, message: "请输入火道温度", trigger: "blur" },
        ],
        waterAjust: [
          { required: true, message: "请输入水分", trigger: "blur" },
        ],
      },
      impChartDataList: [],
      space: {
        bulkDensity: 0.0,
        fineness: 0.0,
        tempeAjust: 0.0,
        timeAjust: 0.0,
        waterAjust: 0.0,
        workspaceCoalRockResultList: [{}, {}, {}],
        workspaceCoalRockList: [],
        //铲数和吨数展示的数据
        shovelTonnageNum: [
          {},
        ]
      },
      favorablePrice: [],
      favorablePriceYear: [],
      entityForm: { ...entityForm },
      dialogCoalEntityFormVisible: false,
      EntityFormIndex: void 0,
      entityFormLoading: false,
      isComputed: false,
      isAddCoal: false,
      coalMatchingData: [{}, {}, {}, {}, {}], // 配煤比例数据
      limit: { ...limit },
      count: 0,
      count1: 0,
      count2: 0,
      count3: 0,
      coalMacRManualList: [],
      coalMacRSpecifyList: [],
      restaurants: [],
      time: "",
      isShow: true,
      config: {},
      activeRowList: [],
      //铲数和吨数字段
      shovel: '铲数',
      // tonnage: '吨数',

      //铲数
      shovelsNumber: {
        totalShovelsNumber: '',
        projectName: '',
        '老母坡': '',
        '王华': '',
        '毛则渠': '',
        '华宁': '',
        '保利裕丰': '',
        '韩咀': '',
        inAd: '',
        inVdaf: '',
        inStd: '',
        inG: '',
        inY: '',
        inX: '',
        theoryMacR0: '',
        inMt: '',  //水分
        qualCsr: '',  //CSR
        theoryMacS: '', //标准差
        date: ''
      },

      //吨数
      tonnageNumber: {
        totalTons: '',
        projectName: '',
        '老母坡': '',
        '王华': '',
        '毛则渠': '',
        '华宁': '',
        '保利裕丰': '',
        '韩咀': '',
        inAd: '',
        inVdaf: '',
        inStd: '',
        inG: '',
        inY: '',
        inX: '',
        theoryMacR0: '',
        inMt: '',  //水分
        qualCsr: '',  //CSR
        theoryMacS: '', //标准差
        date: ''
      },
      projectType: "",
      leftData: [],
      chartId: "",
      rightData: [],
      rateIndex: 0,
      newRateIndex: 0,
      cokeId: "",
      spaceType: "YH",
      targetValue: "",
      chooseInfo: {
        visitable: false,
        record: {},
      },
    };
  },
  computed: {
    getWorkspaceCoalRockList() {
      const countMap = this.space.workspaceCoalRockList.reduce((res, item) => {
        return { ...res, [item.id]: 0 };
      }, {});
      Object.keys(countMap).forEach((key) => {
        this.space.workspaceCoalWashingResultList.forEach((item) => {
          if (item[key] > 0) {
            countMap[key] += 1;
          }
        });
      });
      return this.space.workspaceCoalRockList.filter((item) => {
        return countMap[item.id] > 0;
      });
    },

    workspaceCoalRockListLen() {
      return this.space.workspaceCoalRockList.length;
    },
  },
  created() {
    // 监视space.workspaceCoalRockList，处理source为wb的情况
    this.$watch('space.workspaceCoalRockList', (newVal) => {
      if (newVal && newVal.length > 0) {
        newVal.forEach((item, index) => {
          if (item.source === 'wb') {
            console.log(`将workspaceCoalRockList[${index}].source从wb转换为zy`);
            this.$set(this.space.workspaceCoalRockList[index], 'source', 'zy');
          }
        });
      }
    }, { deep: true, immediate: true });
  },

  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name === "myWash" && to.params && to.params.id) {
        vm.cokeId = to.params.id;
        vm.remakeCoalRock(vm.cokeId);
      } else {
        vm.getNewCoalRock();
      }
    });
  },

  async mounted() {
    this.restaurants = await this.loadingAll();
  },
  methods: {
    reloadPlanList() {
      this.$refs.coalWashingTarget.reload();
    },
    openPlanManageInfo() {
      const target = this.limit;

      this.planManageInfo = {
        visitable: true,
        record: {
          adMax: target.cInAdH,
          stdMax: target.cInStdH,
          vdafMax: target.cInVdafH,

          adTarget: target.cInAd,
          stdTarget: target.cInStd,
          vdafTarget: target.cInVdaf,
          gTarget: target.cInG,
          yTarget: target.cInY,

          adMin: target.cInAdL,
          stdMin: target.cInStdL,
          vdafMin: target.cInVdafL,
          gMin: target.cInGL,
          yMin: target.cInYL,
          csrTarget: target.cJtCsrL,
          csrMin: target.cJtCsrH,
        },
      };
    },
    getSummary(params) {
      const { columns, data } = params;
      const result = [
        { key: "quantity", decimalPlaces: 0, methods: "sum", suffix: "" },
        { key: "percent2", decimalPlaces: 2, methods: "sum", suffix: "%" },
      ].reduce((pre, tar) => {
        const targetMethods =
          tar.methods === "sum"
            ? CalcUtils.calculateSum
            : CalcUtils.calculateAverageDecimal;
        return {
          ...pre,
          [tar.key]: CalcUtils.getShowValue(
            targetMethods(
              data
                .map((item) => item[tar.key])
                .filter((v) => isDef(v))
                .map((v) => Number(v))
            ),
            tar.decimalPlaces,
            "ROUND_HALF_UP",
            tar.suffix
          ),
        };
      }, {});

      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
        } else {
          sums[index] = result[column.property];
        }
      });
      return sums;
    },

    handleTargetValueChange({ val, target }) {
      console.log(target, "target");
      this.limit = {
        ...this.limit,
        cInAdH: target.adMax,
        cInStdH: target.stdMax,
        cInVdafH: target.vdafMax,

        cInAd: target.adTarget,
        cInStd: target.stdTarget,
        cInVdaf: target.vdafTarget,
        cInG: target.gTarget,
        cInY: target.yTarget,

        cInAdL: target.adMin,
        cInStdL: target.stdMin,
        cInVdafL: target.vdafMin,
        cInGL: target.gMin,
        cInYL: target.yMin,

        cJtCsrL: target.csrTarget,
        cJtCsrH: target.csrMin,
      };
    },
    async handleClose(isRefresh, isAdd) {
      this.chooseVisible = false;
      if (isAdd) {
        this.addVisible = false;
        this.coalDetail = {};
        this.addVisibleV = false;
        this.coalDetailV = {};
        await this.$nextTick();
        this.$refs.coal.getList();
      }
      if (isRefresh) {
        this.getNewCoalRock();
      }
    },
    // 数据同步
    async makeSyncCoal(row) {
      const result = {
        id: row.id,
        coalId: row.coalId,
        name: row.name,
        type: row.type, // 煤种
        source: row.source,
        macR0: row.macR0, // R0(%)
        macS: row.macS, // 标准差S
        cleanAd: row.cleanAd,
        cleanVdaf: row.cleanVdaf,
        cleanStd: row.cleanStd,
        procG: row.procG,
        procY: row.procY,
        arrivePrice: row.arrivePrice, // 到厂价（元/吨）
        procX: row.procX,
        qualCsr: row.qualCsr,
        cleanMt: row.cleanMt
      };

      await this.$confirm("保存成功会同步该条数据信息，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });
      const workspace = result;
      this.isLoading = true;
      const res = await Func.fetch(syncCoal, workspace);
      this.isLoading = false;
      if (res) {
        this.$message({
          type: "success",
          message: "该数据信息同步成功",
        });
      }
    },
    // 添加到我的煤源
    async addToMyCoal(row) {
      try {
        await this.$confirm("保存成功会同步该条数据信息，是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        
        // 确保source字段处理正确 - 修复源码类型映射
        let sourceType = row.source;
        // 如果source是'wb'字符串，转换为'zy'
        if (sourceType === 'wb') {
          sourceType = 'zy';
        } else {
          sourceType = COAL_SOURCE_TYPE.WAI_BU; // 默认外部煤源
        }
        
        // 确保运费数据被正确获取
        const transportPrice = row.activePriceTransportPriceNoTax || row.transitFee || row.transportFee || row.freightFee || row.freight || row.roadCost || 0;
        
        console.log('运费数据来源:', {
          activePriceTransportPriceNoTax: row.activePriceTransportPriceNoTax,
          transitFee: row.transitFee,
          transportFee: row.transportFee,
          freightFee: row.freightFee,
          freight: row.freight,
          roadCost: row.roadCost
        });
        console.log('最终使用的运费值:', transportPrice);
        
        const data = {
          id: row.id,
          coalId: row.coalId,
          name: row.name,
          type: row.type,
          source: sourceType, // 使用转换后的sourceType
          macR0: row.macR0,
          macS: row.macS,
          cleanAd: row.cleanAd,
          cleanVdaf: row.cleanVdaf,
          cleanStd: row.cleanStd,
          procG: row.procG,
          procY: row.procY,
          factoryPrice: row.factoryPrice,
          // 添加多个可能的运费字段名称，确保至少一个能被后端识别
          activePriceTransportPriceNoTax: transportPrice,
          transitFee: transportPrice,
          transportFee: transportPrice,
          freightFee: transportPrice,
          freight: transportPrice,
          roadCost: transportPrice,
          arrivePrice: row.arrivePrice,
          procX: row.procX,
          qualCsr: row.qualCsr,
          cleanMt: row.cleanMt
        };
        
        console.log('添加到我的煤源:', data);
        
        this.isLoading = true;
        this.loadingText = '正在添加到我的煤源...';
        const res = await Func.fetch(addToMyCoalSource, data);
        this.isLoading = false;
        
        if (res) {
          this.$message({
            type: "success",
            message: "数据信息已保存至我的煤源中",
          });
          
          // 发送事件通知父组件刷新煤源数据
          this.$emit('refresh-coal-source');
          
          // 刷新煤种列表以显示最新数据
          this.getNewCoalRock();
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error("添加失败：" + (error.message || "未知错误"));
        }
        console.error("添加煤种到我的煤源失败:", error);
      } finally {
        this.isLoading = false;
        this.loadingText = '';
      }
    },
    // 更改表头颜色
    activeHeader() {
      return "background:#2F79E8; color:#fff";
    },
    columnStyle({ row, column, rowIndex, columnIndex }) {
      // 加上第一行和动态的workspaceCoalRockListLen长度
      if (columnIndex < this.workspaceCoalRockListLen + 1) {
        return "background: #fff6e6";
      }
      return "";
    },
    // 更改表体颜色
    activeRow({ row, rowIndex }) {
      if (rowIndex % 2 === 1) {
        return "background: #F3FCFD";
      } else if (rowIndex % 2 === 0) {
        return "background: #fff";
      }
      return "";
    },
    async remakeCoalRock(id) {
      // 保存当前用户输入的指标值
      const savedLimit = { ...this.limit };
      
      this.isLoading = true;
      const res = await Func.fetch(remakeNewCoal, {
        type: "YH",
        coalWashingProjectId: id,
      });
      if (res) {
        this.active = 4;
        this.isLoading = false;
        this.space = { ...res.data };
        this.limit = { ...this.space };
        // 动态显示煤种表头 数据处理
        this.space.workspaceCoalRockList.map((item, index) => {
          let id = item.id;
          this.space.workspaceCoalWashingResultList.map((wash) => {
            wash[id] = wash.coalPercentList[index];
            return wash;
          });
        });
        // 当页面查询移除或者添加煤种时  重新计算煤种配比
        let number = 0;
        if (this.space.workspaceCoalRockList) {
          this.space.workspaceCoalRockList.map((item) => {
            number = Number(item.quantity) + number;
          });
          this.space.workspaceCoalRockList.map((item) => {
            if (item.quantity) {
              item.percent2 = ((item.quantity * 100) / number).toFixed(2);
            } else {
              item.percent2 = 0;
            }
            return item;
          });
        }
        // 添加Echart数据
        this.impChartDataList = [];
        let chartObj = {};
        chartObj.id = "rg";
        chartObj.xName = "区间";
        chartObj.type = "bar";
        chartObj.color = "#F8A20F";
        chartObj.yData = res.data.proportionManualList;
        chartObj.xData = res.data.rangeManualList;
        this.impChartDataList.push(chartObj);
        let chartObj1 = {};
        chartObj1.name = "智能";
        chartObj1.xName = "区间";
        chartObj1.type = "bar";
        chartObj1.color = "#FF726B";
        chartObj1.yData = res.data.proportionSpecifyList;
        chartObj1.xData = res.data.rangeSpecifyList;
        this.impChartDataList.push(chartObj1);
        // echart下面列表显示
        this.leftData = res.data.coalTypeProportionManualList;
        this.rightData = res.data.coalTypeProportionSpecifyList;
        // 反射率列表显示
        let obj = {};
        let obj2 = {};
        let obj3 = {};
        let extlist = [
          {
            rangeCount: "人工",
            rangeName: "区间",
            sort: null,
          },
        ];
        let extlist2 = [
          {
            rangeCount: "智能",
            rangeName: "区间",
            sort: null,
          },
        ];
        this.rateList = [];
        this.coalMacRManualList = [...extlist, ...res.data.coalMacRManualList];
        this.coalMacRSpecifyList = [
          ...extlist2,
          ...res.data.coalMacRSpecifyList,
        ];
        if (res.data.coalMacRSpecifyList.length > 0) {
          this.coalMacRSpecifyList.map((coal) => {
            obj3[coal.rangeName] = coal.rangeCount;
            obj2[coal.rangeName] = coal.rangeName;
          });
          this.rateList = [{ ...obj2 }, { ...obj3 }];
        }
        if (res.data.coalMacRManualList.length > 0) {
          this.coalMacRManualList.map((coal) => {
            obj2[coal.rangeName] = coal.rangeName;
            obj[coal.rangeName] = coal.rangeCount;
          });
          this.rateList = [{ ...obj2 }, { ...obj }];
        }
        // if (obj2 && obj && obj3) {
        //   this.rateList = [{...obj2}, {...obj}, {...obj3}]
        // }
      }
    },

    // 点击行事件
    async handleRowClick(row) {
      // 检查是否是第一次点击该方案
      if (!this.firstClickFlags.has(row.projectName)) {
        // 标记为已点击
        this.firstClickFlags.add(row.projectName);
        // 设置totalTons为100
        row.totalTons = 100;
      }

      // 获取配煤数据
      const coalPercentContent = this.getWorkspaceCoalRockList.map(coal => ({
        name: coal.name,
        value: row[coal.id]
      }));
      // 设置发送的coalPercentContent参数为固定配煤方案
      this.initialCoalPercentContent = coalPercentContent;
      // const postData = {
      //   coalPercentContent: JSON.stringify(coalPercentContent),
      //   projectName: row.projectName,
      //   inAd: row.inAd,
      //   inVdaf: row.inVdaf,
      //   inStd: row.inStd,
      //   inG: row.inG,
      //   inY: row.inY,
      //   inX: row.inX,
      //   theoryMacR0: row.theoryMacR0,
      //   inMt: row.inMt,
      //   qualCsr: row.qualCsr,
      //   theoryMacS: row.theoryMacS,
      //   shovelsNumberContent: JSON.stringify(coalPercentContent),
      //   date: new Date().toLocaleDateString("zh-CN", {
      //     year: "numeric",
      //     month: "2-digit",
      //     day: "2-digit",
      //   }).replace(/\//g, "-"),
      // };
      // 调用保存接口
      // await Func.fetch(saveData, postData);



      // // // 2. 更新界面显示
      // const baseData = {
      //   name: this.shovel,
      //   projectName: row.projectName,
      //   totalShovelsNumber: row.totalShovelsNumber || this.shovelsNumber.totalShovelsNumber,
      //   inAd: row.inAd,
      //   inVdaf: row.inVdaf,
      //   inStd: row.inStd,
      //   inG: row.inG,
      //   inY: row.inY,
      //   inX: row.inX,
      //   theoryMacR0: row.theoryMacR0,
      //   inMt: row.inMt,
      //   qualCsr: row.qualCsr,
      //   theoryMacS: row.theoryMacS,
      //   date: new Date().toLocaleDateString("zh-CN", {
      //     year: "numeric",
      //     month: "2-digit",
      //     day: "2-digit",
      //   }).replace(/\//g, "-"),
      // };

      // this.shovelsNumber = {
      //   ...baseData,
      //   name: this.shovel,
      //   shovelsNumberContent: JSON.stringify(coalPercentContent),
      //   totalShovelsNumber: row.totalShovelsNumber
      // }
      // this.tonnageNumber = {
      //   ...baseData,
      //   name: this.tonnage,
      //   tonsContent: JSON.stringify(coalPercentContent),
      //   totalTons: row.totalTons
      // }
      // this.space.shovelTonnageNum = [this.shovelsNumber, this.tonnageNumber];


      // this.getshovelTonnageData(row)       //调用返回的数据显示到界面


      // 删除后端的数据
      await Func.fetch(deleteProjectName, {
        projectName: row.projectName,
        date: new Date().toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        }).replace(/\//g, "-")
      });


      this.getshovelTonnageData(row);


      this.isLoading = false;
      let chartObj1 = {};
      chartObj1.name = row.projectName;
      chartObj1.xName = "区间";
      chartObj1.type = "bar";
      chartObj1.yData = row.proportionList;
      chartObj1.xData = row.rangeList;
      this.impChartDataList = [
        { ...this.impChartDataList[0] },
        { ...chartObj1 },
      ];
      this.rightData = row.coalTypeProportionList;
      let obj3 = {};
      // let extlist = [{
      //   rangeCount: '人工',
      //   rangeName: '区间',
      //   sort: null
      // }]
      let extlist2 = [
        {
          rangeCount: "智能",
          rangeName: "区间",
          sort: null,
        },
      ];
      this.coalMacRManualList = [...extlist2, ...row.coalMacRList];
      this.coalMacRManualList.map((coal) => {
        obj3[coal.rangeName] = coal.rangeCount;
      });
      if (this.rateList.length === 2) {
        this.rateList = [{ ...this.rateList[0] }, { ...obj3 }];
      }
      if (this.rateList.length === 3) {
        this.rateList = [
          { ...this.rateList[0] },
          { ...this.rateList[1] },
          { ...obj3 },
        ];
      }
    },
    // 自定义合计算法
    showDiff(param) {
      const { data } = param;
      let row = data[0];
      let length = 0;
      if (this.space.workspaceCoalRockList.length) {
        length = this.space.workspaceCoalRockList.length;
      }
      // 获取煤种动态长度
      const sums = [];
      if (this.activeRowList && row) {
        sums[length] = "-";
        sums[0] = "对比";
        if (row.inAd && this.activeRowList.inAd) {
          let index = Number(length) + 1;
          sums[index] = (row.inAd - this.activeRowList.inAd).toFixed(2);
          // 根据煤种动态长度显示  (本来写循环显示但是好像死循环)
        }
        if (row.inVdaf && this.activeRowList.inVdaf) {
          let index = Number(length) + 2;
          sums[index] = (row.inVdaf - this.activeRowList.inVdaf).toFixed(2);
        }
        if (row.inStd && this.activeRowList.inStd) {
          let index = Number(length) + 3;
          sums[index] = (row.inStd - this.activeRowList.inStd).toFixed(2);
        }
        if (row.inG && this.activeRowList.inG) {
          let index = Number(length) + 4;
          sums[index] = (row.inG - this.activeRowList.inG).toFixed(2);
        }
        if (row.inY && this.activeRowList.inY) {
          let index = Number(length) + 5;
          sums[index] = (row.inY - this.activeRowList.inY).toFixed(2);
        }
        if (row.inX && this.activeRowList.inX) {
          let index = Number(length) + 6;
          sums[index] = (row.inX - this.activeRowList.inX).toFixed(2);
        }
        if (row.inR0 && this.activeRowList.inR0) {
          let index = Number(length) + 7;
          sums[index] = (row.inR0 - this.activeRowList.inR0).toFixed(2);
        }
        if (row.theoryMacS && this.activeRowList.theoryMacS) {
          let index = Number(length) + 8;
          sums[index] = (
            row.theoryMacS - this.activeRowList.theoryMacS
          ).toFixed(2);
        }
        if (row.inPrice && this.activeRowList.inPrice) {
          let index = Number(length) + 9;
          sums[index] = (row.inPrice - this.activeRowList.inPrice).toFixed(2);
        }
        let endIndex = Number(length) + 10;
        sums[endIndex] = "-";
      }
      return sums;
    },
    onFcous() {
      this.space.workspaceCoalRockList.map((item) => {
        if (Number(item.quantity) === 0) {
          item.quantity = "";
        }
        return item;
      });
    },
    loseFcous(index, row, column) {
      let number = 0;
      this.space.workspaceCoalRockList.map((item) => {
        number = Number(item.quantity) + number;
      });
      this.space.workspaceCoalRockList.map((item) => {
        if (item.quantity > 0) {
          item.percent2 = ((item.quantity * 100) / number).toFixed(2);
        } else {
          item.percent2 = 0;
        }
        return item;
      });
    },
    /*
     * 单选增加煤种
     * */
    async choose(coal) {
      this.dialogFormVisible = false;
      this.resetChecked();
      const res = await Func.fetch(addCoalWashing, {
        spaceType: this.spaceType,
        coalIdList: [coal.id],
      });
      if (res) {
        this.isAddCoal = true;
        if (res) {
          this.isAddCoal = true;
          if (!res.data.length) return false;
          // 保留用户输入的limit数据
          await this.getNewCoalRock();
        }
      }
    },

    //保存铲数和吨数
    async sendCoalPercentData(row) {


      // 2. 准备上传数据
      // 动态获取煤种列表并构造煤种比例数据
      const coalPercentContent = [];
      const shovelsNumberContent = [];
      const tonsContent = [];
      this.getWorkspaceCoalRockList.forEach(coal => {
        coalPercentContent.push({
          name: coal.name,
          value: row[coal.name]
        })
        shovelsNumberContent.push({
          name: coal.name,
          value: this.space.shovelTonnageNum[0][coal.name]
        })
        tonsContent.push({
          name: coal.name,
          value: this.space.shovelTonnageNum[1][coal.name]
        })
      });

      // 构造请求参数
      const postData = {
        coalPercentContent: JSON.stringify(this.initialCoalPercentContent),
        projectName: row.projectName,
        inAd: row.inAd,
        inVdaf: row.inVdaf,
        inStd: row.inStd,
        inG: row.inG,
        inY: row.inY,
        inX: row.inX,
        theoryMacR0: row.theoryMacR0,
        inMt: row.inMt,
        qualCsr: row.qualCsr,
        theoryMacS: row.theoryMacS,
        shovelsNumberContent: JSON.stringify(shovelsNumberContent),
        tonsContent: JSON.stringify(tonsContent),
        date: row.date || new Date().toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        }).replace(/\//g, "-"),
      };
      postData.totalShovelsNumber = this.space.shovelTonnageNum[0].totalShovelsNumber || '';
      // 判断每个方案是否是第一次点击
      postData.totalTons = this.space.shovelTonnageNum[1].totalTons || 100;
      // 调用保存接口
      const res = await Func.fetch(saveData, postData);

      if (res) {
        // 保存成功后获取最新数据
        await this.getshovelTonnageData(row);
      }
    },















    //获取返回的铲数和吨数的数据
    async getshovelTonnageData(row) {
      try {
        this.isLoading = true;
        this.loadingText = "加载中...";

        // 获取铲数和吨数数据
        const res = await Func.fetch(getshovelTonnage, {
          projectName: row.projectName,
          date: new Date().toLocaleDateString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
          }).replace(/\//g, "-")
        });
        if (res.data.records && res.data.records.length > 0) {
          if (res.data.records) {
            const record = res.data.records[0];

            // 如果是第一次点击，强制设置totalTons为100
            if (!this.firstClickFlags.has(row.projectName)) {
              record.totalTons = 100;
            }

            // 初始化铲数和吨数数据
            const shovelData = { name: '铲数' };
            const tonnageData = { name: '吨数', totalTons: record.totalTons };

            // 处理铲数数据
            if (record.shovelsNumberContent) {
              const shovelContent = JSON.parse(record.shovelsNumberContent);
              shovelContent.forEach(item => {
                shovelData[item.name] = item.value;
              });
              shovelData.totalShovelsNumber = record.totalShovelsNumber;
              shovelData.projectName = record.projectName;
              shovelData.inAd = record.inAd;
              shovelData.inVdaf = record.inVdaf;
              shovelData.inStd = record.inStd;
              shovelData.inG = record.inG;
              shovelData.inY = record.inY;
              shovelData.inX = record.inX;
              shovelData.theoryMacR0 = record.theoryMacR0;
              shovelData.inMt = record.inMt;
              shovelData.qualCsr = record.qualCsr;
              shovelData.theoryMacS = record.theoryMacS;
              shovelData.date = record.date;
            }
            // 处理吨数数据
            if (record.tonsContent) {
              const tonsContent = JSON.parse(record.tonsContent);
              tonsContent.forEach(item => {
                tonnageData[item.name] = item.value;
              });
              tonnageData.totalTons = record.totalTons;
              tonnageData.projectName = record.projectName;
              tonnageData.inAd = record.inAd;
              tonnageData.inVdaf = record.inVdaf;
              tonnageData.inStd = record.inStd;
              tonnageData.inG = record.inG;
              tonnageData.inY = record.inY;
              tonnageData.inX = record.inX;
              tonnageData.theoryMacR0 = record.theoryMacR0;
              tonnageData.inMt = record.inMt;
              tonnageData.qualCsr = record.qualCsr;
              tonnageData.theoryMacS = record.theoryMacS;
              tonnageData.date = record.date;
            }
            // 更新界面数据
            // this.shovelsNumber = { ...shovelData };
            // // this.space.shovelTonnageNum = [shovelData, this.tonnageNumber];
            // this.tonnageNumber = { ...tonnageData };
            this.space.shovelTonnageNum = [shovelData, tonnageData];

            // this.sendCoalPercentData(row);
          }
        } else {
          this.space.shovelTonnageNum = [{ name: '总数' }, { name: '铲数' }];
          this.sendCoalPercentData(row);
        }
        return null;
      } catch (err) {
        console.error("数据获取失败:", err);
        return null;
      } finally {
        this.isLoading = false;
      }
    },

    /*
     * 多选增加煤种
     * */
    async addCoals(list) {
      const checkBoxGather = this.$refs.coal.checkBoxGather;
      const resultGather = [];
      for (let i in checkBoxGather) {
        if (checkBoxGather.hasOwnProperty(i) && checkBoxGather[i]) {
          resultGather.push(i);
        }
      }
      if (list.length === 0) {
        this.$message.error("至少选择一个");
        return;
      }
      const res = await Func.fetch(addCoalWashing, {
        spaceType: this.spaceType,
        coalIdList: list,
      });
      this.resetChecked();
      if (res) {
        this.isAddCoal = true;
        if (!res.data.length) return false;
        // 保留用户输入的limit数据
        await this.getNewCoalRock();
      }
    },
    /*
     * 重置多选状态并隐藏对话框
     * */
    resetChecked() {
      const checkBoxGather = this.$refs.coal.checkBoxGather;
      for (let i in checkBoxGather) {
        if (checkBoxGather.hasOwnProperty(i)) {
          checkBoxGather[i] = false;
        }
      }
      this.dialogFormVisible = false;
    },
    /*
     * 显示选煤对话框
     * */
    add() {
      // this.chooseVisible = true
      // console.log('uuuu')
      // console.log(this.$refs.coal)
      // this.$nextTick(() => {
      //   this.$refs.coal.$refs.coal.getList()
      // })
      // if (this.$refs.coal) {
      //   this.$refs.coal.$refs.coal.getList()
      // }
      // this.$refs.coal.$refs.coal.clearSelection()
      // this.$router.push('/coal?activeTitle=MWASH')

      this.chooseInfo = {
        visitable: true,
        record: {},
      };
    },
    /*
     * 移除一行数据
     * */
    async remove(row) {
      await this.$confirm("移除煤种会清空当前方案数据，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      });

      // 保存当前所有煤种的编辑数据（除了要删除的那一行）
      const savedCoalData = {};
      if (this.space && this.space.workspaceCoalRockList) {
        this.space.workspaceCoalRockList.forEach(item => {
          if (item.id !== row.id) { // 不保存要删除的行
            savedCoalData[item.id] = {
              quantity: item.quantity,
              percentCond: item.percentCond,
              percent1: item.percent1,
              macR0: item.macR0,
              macS: item.macS,
              cleanAd: item.cleanAd,
              cleanVdaf: item.cleanVdaf,
              cleanStd: item.cleanStd,
              procG: item.procG,
              procY: item.procY,
              factoryPrice: item.factoryPrice,
              activePriceTransportPriceNoTax: item.activePriceTransportPriceNoTax,
              arrivePrice: item.arrivePrice,
              procX: item.procX,
              cleanMt: item.cleanMt,
              qualCsr: item.qualCsr
            };
          }
        });
      }

      this.isLoading = true;
      const res = await Func.fetch(deleteWashingById, {
        id: row.id,
        spaceType: "YH",
      });
      this.isLoading = false;
      if (res) {
        this.$message({
          type: "success",
          message: "删除成功",
        });

        // 先在本地删除已移除的煤种
        if (this.space && this.space.workspaceCoalRockList) {
          // 记录被删除的煤种名称
          const deletedName = row.name;
          console.log(`正在本地删除煤种: ${deletedName}`);

          // 从本地数据中过滤掉该煤种
          this.space.workspaceCoalRockList = this.space.workspaceCoalRockList.filter(
            item => item.id !== row.id
          );
        }

        // 然后再重新加载数据以确保与后端同步，并传入保存的编辑数据
        this.getNewCoalRock(savedCoalData);
        this.impChartDataList = [{}, {}];
        this.rateList = [];
      }
    },

    /*
     * 获取数据
     * */
    async getNewCoalRock(savedCoalData = null) {
      // 保存当前用户输入的指标值，避免刷新后丢失
      const savedLimit = { ...this.limit };
      
      this.isLoading = true;
      const res = await Func.fetch(getNewCoalRock, { type: "YH" });
      // console.log(res)
      // let extList = res.data.rangeList
      // let list = res.data.workspaceCoalRockList
      // if (list.length > 0) {
      //     this.active = 3
      // }
      // if (list.length > 0 && extList.length > 0) {
      //     this.active = 4
      // } else {
      //     this.active = 2
      // }
      if (res.data) {
        let newExtList = res.data.workspaceCoalWashingResultList;
        let newlist = res.data.workspaceCoalRockList;
        if (newlist.length > 0) {
          this.active = 3;
        }
        if (newlist.length > 0 && newExtList.length > 0) {
          this.active = 4;
        } else {
          this.active = 2;
        }
        this.isLoading = false;
        
        // 确保每个煤种的source字段正确设置
        if (res.data.workspaceCoalRockList && res.data.workspaceCoalRockList.length > 0) {
          res.data.workspaceCoalRockList.forEach(item => {
            // 修复wb到zy的转换
            if (item.source === 'wb') {
              item.source = 'zy';
            }
            // 对于掌上煤焦，优先设置为掌上煤焦
            else if (item.coalSource === 'ZSMJ' || item.coalSource === 'zsmj') {
              item.source = COAL_SOURCE_TYPE.ZSMJ;
              console.log(`设置掌上煤焦: ${item.name}, source=${item.source}`);
            }
            // 对于可以添加到我的煤源的行（有保存按钮的行），如果不是掌上煤焦则设置为外部煤源
            else if (item.dataType !== 'nb' && item.source !== COAL_SOURCE_TYPE.ZSMJ) {
              item.source = COAL_SOURCE_TYPE.WAI_BU;
            }
            // 其他情况下，检查并纠正煤源类型
            else if (item.coalSource === 'WAI_BU' || item.coalSource === 'wb') {
              item.source = COAL_SOURCE_TYPE.WAI_BU;
            } else if (item.coalSource === 'ZI_YOU' || item.coalSource === 'zy') {
              item.source = COAL_SOURCE_TYPE.ZI_YOU;
            }
            
            // 如果source字段不存在或不正确，根据其他字段推断
            if (!item.source || (item.source !== COAL_SOURCE_TYPE.WAI_BU && 
                item.source !== COAL_SOURCE_TYPE.ZI_YOU && 
                item.source !== COAL_SOURCE_TYPE.ZSMJ)) {
              // 默认设为外部煤源
              item.source = COAL_SOURCE_TYPE.WAI_BU;
            }
            
            // 确保运费字段一致性
            const transportPrice = item.activePriceTransportPriceNoTax || item.transitFee || item.transportFee || item.freightFee || item.freight || item.roadCost || 0;
            item.activePriceTransportPriceNoTax = transportPrice;
            item.transitFee = transportPrice;
            item.transportFee = transportPrice;
            item.freightFee = transportPrice;
            item.freight = transportPrice;
            item.roadCost = transportPrice;
          });
        }
        
        this.space = { ...res.data, shovelTonnageNum: [{ name: '铲数' }, { name: '吨数' }] };

        // 恢复之前保存的煤种编辑数据
        if (savedCoalData && this.space.workspaceCoalRockList) {
          this.space.workspaceCoalRockList.forEach(item => {
            if (savedCoalData[item.id]) {
              const savedItem = savedCoalData[item.id];
              // 恢复所有可编辑的字段
              item.quantity = savedItem.quantity;
              item.percentCond = savedItem.percentCond;
              item.percent1 = savedItem.percent1;
              item.macR0 = savedItem.macR0;
              item.macS = savedItem.macS;
              item.cleanAd = savedItem.cleanAd;
              item.cleanVdaf = savedItem.cleanVdaf;
              item.cleanStd = savedItem.cleanStd;
              item.procG = savedItem.procG;
              item.procY = savedItem.procY;
              item.factoryPrice = savedItem.factoryPrice;
              item.activePriceTransportPriceNoTax = savedItem.activePriceTransportPriceNoTax;
              item.arrivePrice = savedItem.arrivePrice;
              item.procX = savedItem.procX;
              item.cleanMt = savedItem.cleanMt;
              item.qualCsr = savedItem.qualCsr;
            }
          });
        }

        // 恢复之前保存的指标录入数据，而不是用space完全覆盖limit
        // 只更新limit中为空的值，保留用户已输入的值
        const newLimit = {};
        Object.keys(savedLimit).forEach(key => {
          // 如果用户之前有输入值，则保留
          if (savedLimit[key] !== null && savedLimit[key] !== undefined && savedLimit[key] !== '') {
            newLimit[key] = savedLimit[key];
          } else {
            // 否则使用从后端获取的默认值
            newLimit[key] = this.space[key] || null;
          }
        });
        this.limit = newLimit;
        
        // 动态显示煤种表头 数据处理
        this.space.workspaceCoalRockList.map((item, index) => {
          let id = item.id;
          this.space.workspaceCoalWashingResultList.map((wash) => {
            wash[id] = wash.coalPercentList[index];
            return wash;
          });
        });
        // 当页面查询移除或者添加煤种时  重新计算煤种配比
        let number = 0;
        if (this.space.workspaceCoalRockList) {
          this.space.workspaceCoalRockList.map((item) => {
            number = Number(item.quantity) + number;
          });
          this.space.workspaceCoalRockList.map((item) => {
            if (item.quantity) {
              item.percent2 = ((item.quantity * 100) / number).toFixed(2);
            } else {
              item.percent2 = 0;
            }
            return item;
          });
        }
        // 添加Echart数据
        this.impChartDataList = [];
        let chartObj = {};
        chartObj.name = "人工";
        chartObj.color = "#FF726B";
        chartObj.xName = "区间";
        chartObj.type = "bar";
        chartObj.yData = res.data.proportionManualList;
        chartObj.xData = res.data.rangeManualList;
        this.impChartDataList.push(chartObj);
        let chartObj1 = {};
        chartObj1.name = "智能";
        chartObj1.xName = "区间";
        chartObj1.type = "bar";
        chartObj1.color = "#33CAD9";
        chartObj1.yData = res.data.proportionSpecifyList;
        chartObj1.xData = res.data.rangeSpecifyList;
        this.impChartDataList.push(chartObj1);
        // echart下面列表显示
        this.leftData = res.data.coalTypeProportionManualList;
        this.rightData = res.data.coalTypeProportionSpecifyList;

        // 反射率列表显示
        let obj = {};
        let obj2 = {};
        let obj3 = {};
        let extlist = [
          {
            rangeCount: "人工",
            rangeName: "区间",
            sort: null,
          },
        ];
        let extlist2 = [
          {
            rangeCount: "智能",
            rangeName: "区间",
            sort: null,
          },
        ];
        this.coalMacRManualList = [...extlist, ...res.data.coalMacRManualList];
        this.coalMacRSpecifyList = [
          ...extlist2,
          ...res.data.coalMacRSpecifyList,
        ];
        if (res.data.coalMacRSpecifyList.length > 0) {
          this.rateList = [];
          this.coalMacRSpecifyList.map((coal, index) => {
            obj3[coal.rangeName] = coal.rangeCount;
            obj2[coal.rangeName] = coal.rangeName;
            if (coal.rangeCount > 0) {
              this.rateIndex = index + 1;
              return true;
            }
          });
          this.rateList = [{ ...obj2 }, { ...obj3 }];
        }
        if (res.data.coalMacRManualList.length > 0) {
          this.rateList = [];
          this.coalMacRManualList.map((coal, index) => {
            obj2[coal.rangeName] = coal.rangeName;
            obj[coal.rangeName] = coal.rangeCount;
            if (coal.rangeCount > 0) {
              this.newRateIndex = index + 1;
            }
          });
          this.rateList = [{ ...obj2 }, { ...obj }];
        }
        if (
          res.data.coalMacRSpecifyList.length > 0 &&
          res.data.coalMacRManualList.length > 0
        ) {
          this.rateList = [];
          this.coalMacRSpecifyList.map((coal) => {
            obj3[coal.rangeName] = coal.rangeCount;
          });
          this.coalMacRManualList.map((coal) => {
            obj2[coal.rangeName] = coal.rangeName;
            obj[coal.rangeName] = coal.rangeCount;
          });
          this.rateList = [{ ...obj2 }, { ...obj }, { ...obj3 }];
        }
      }
    },
    /*
     * 计算操作
     * */
    calculate: async function (formName, type) {
      // 至少选择一条数据
      if (this.space.workspaceCoalRockList.length === 0) {
        this.$message({
          message: "至少选择一条来源数据",
          type: "error",
          showClose: true,
        });
        return false;
      }
      // 至少选择一条数据
      let message = "";
      let valid = true;
      // // 列表字段校验
      this.space.workspaceCoalRockList.forEach((val) => {
        if (val.cleanAd < 0) {
          valid = false;
          message = val.name + "Ad不能为负值";
          return false;
        }

        if (val.cleanVdaf < 0) {
          valid = false;
          message = val.name + "Vdaf不能为负值";
          return false;
        }

        if (val.cleanStd < 0) {
          valid = false;
          message = val.name + "St,d不能为负值";
          return false;
        }

        if (val.procG < 0) {
          valid = false;
          message = val.name + "G不能为负值";
          return false;
        }

        if (val.procY < 0) {
          valid = false;
          message = val.name + "Y不能为负值";
          return false;
        }

        if (val.procX < 0) {
          valid = false;
          message = val.name + "X不能为负值";
          return false;
        }

        if (val.macR0 < 0) {
          valid = false;
          message = val.name + "R0不能为负值";
          return false;
        }

        // if (!val.factoryPrice) {
        //   valid = false;
        //   message = val.name + " 含税煤价不能为空";
        //   return false;
        // }

        // if (!val.activePriceTransportPriceNoTax) {
        //   valid = false;
        //   message = val.name + " 运费不能为空";
        //   return false;
        // }

        if (!val.arrivePrice) {
          valid = false;
          message = val.name + " 配煤成本不能为空";
          return false;
        }
      });
      if (!valid) {
        this.$message({
          message: message,
          type: "error",
          showClose: true,
        });
        return false;
      }
      // console.log(message)
      // valid = await this.$refs[formName].validate()
      // if (!valid) {
      //   this.$message.error(message)
      //   return false
      // }
      //
      const data = { ...this.space, ...this.limit };
      if (type === "OptTypeSpecify" || type === "OptTypeManual") {
        this.loadingText = "正在努力计算中,请稍后...";
      }
      this.isLoading = true;
      const _data = { ...data, ...this.limit, optimizeType: type };
      const res = await Func.fetch(coalWashingQualityOptimizeByType, _data);
      this.isLoading = false;
      this.loadingText = "";
      if (res) {
        this.getNewCoalRock();
        this.active = 4;
      }
    },
    /*
     * 打开修改煤种信息对话框
     * @param row
     * */
    edit(row) {
      // console.log('外部煤源')
      // 传入煤种对id
      this.coalDetail = { ...row, id: row.coalId };
      this.addVisible = true;
      // this.$router.push({
      //     name: 'myDetails',
      //     params: {row, isShow: true, title: row.name, current: 'CoalDetails'}
      // })
    },
    editV(row) {
      // console.log('自有煤源')
      const deatils = { ...row, id: row.coalId }; // 传入煤种对id
      this.coalDetailV = deatils;
      this.addVisibleV = true;
    },

    /*
     * 关闭修改煤种信息对话框并重置状态
     * */
    closeDialog() {
      this.entityForm = { ...entityForm };
      this.$refs.entityForm.$children[0].resetFields();
      this.dialogCoalEntityFormVisible = false;
    },
    /*
     * 保存修改状态
     * */
    async entityFormSave() {
      const entityForm = this.$refs.entityForm.$children[0];
      if (await entityForm.validate()) {
        this.entityFormLoading = true;
        const data = { ...entityForm.model };
        this.space.workspaceCoalRockList.splice(this.EntityFormIndex, 1, data);
        this.entityFormLoading = false;
        this.closeDialog();
      }
    },
    /*
     * 显示保存方案对话框
     * */
    async save(index) {
      this.dialogProjectVisible = true;
      this.projectForm.index = index;
      this.$nextTick(() => {
        this.$refs["projectNameForm"].resetFields();
      });
    },
    /*
     * 关闭保存方案对话框并重置状态
     * */
    closeProjectNameForm() {
      this.$refs["projectNameForm"].resetFields();
      this.dialogProjectVisible = false;
    },
    /*
     * 保存方案
     * */
    async saveProject() {
      const valid = await this.$refs["projectNameForm"].validate();
      if (!valid) {
        return false;
      }
      let data = { ...this.space };
      if (this.isOptimization) {
        data = { ...data, ...this.limit };
      }
      data.workspaceCoalWashingResultList =
        data.workspaceCoalWashingResultList.map((val) => {
          delete val.coal;
          return val;
        });
      data.forecastItemDto =
        data.workspaceCoalWashingResultList[this.projectForm.index];
      data.projectName = this.projectForm.name;
      data.projectType = this.projectForm.projectType;
      data.spaceType = this.spaceType;
      // 是否跟踪
      data.isTrace = "Y";
      delete data.workspaceCoalWashingResultList;
      const res = await Func.fetch(saveWashProjectApi, data);
      this.isLoading = false;
      if (res) {
        this.$message({
          showClose: true,
          message: "保存成功",
          type: "success",
        });
        this.closeProjectNameForm();
      }
    },
    /*
     * 清空列表
     * */
    async clearList() {
      this.isLoading = true;
      const res = await Func.fetch(removeWashAllCoal, {
        spaceType: this.spaceType,
      });
      this.isLoading = false;
      if (res) {
        // 重置limit对象为初始空值状态
        this.limit = { ...limit };
        this.getNewCoalRock();
        this.active = 2;
      }
    },
    /*
     * 配比列头渲染函数
     * */
    percentColRender(createElement, { column, $index }) {
      const { label, property } = column;
      return createElement(
        "span",
        {
          style: {
            "line-height": "22px",
            "padding-right": "0",
            color: "#000",
          },
        },
        [
          label,
          label !== "指定" && property === "percent2"
            ? `(${this.count.toFixed()})%`
            : label !== "人工" && property === "percent1"
              ? `(${this.count1.toFixed()})%`
              : property === "percent5"
                ? `(${this.count2.toFixed()})%`
                : `(${this.count3.toFixed()})%`,
          createElement("el-button", {
            attrs: {
              size: "mini",
              icon: "el-icon-delete",
            },
            style: {
              float: "right",
            },
            on: {
              click: (_) => {
                let { workspaceCoalRockList } = this.space;
                workspaceCoalRockList = workspaceCoalRockList.map((val) => {
                  val[property] = 0;
                  return val;
                });
                this.space.workspaceCoalRockList = [...workspaceCoalRockList];
              },
            },
          }),
        ]
      );
    },
    /*
     *  保存方案公司信息
     * */
    async loadingAll() {
      const res = await Func.fetch(getWashName, {
        projectType: "LL",
        jtAd: 12,
        jtStd: 0.6,
      });
      // console.log(res.data)
      return res.data;

      // return [
      //   { 'name': '执行-Ad-13-S-0.7', 'projectType': 'ZX' },
      //   { 'name': '试验-Ad-13-S-0.7', 'projectType': 'SY' },
      //   { 'name': '理论-Ad-13-S-0.7', 'projectType': 'LL' }
      // ]
    },
    querySearch(queryString, cb) {
      const restaurants = this.restaurants;
      const results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurants) => {
        return restaurants.name.indexOf(queryString) === 0;
      };
    },
    handleSelect(item) {
      this.projectForm.name = item.name;
    },
    handleSaveCoal(row) {
      // 判断煤种标签类型
      // 检查是否有"外"标签(source为WAI_BU或dataType为wb)
      if (row.dataType === 'wb' || row.source === COAL_SOURCE_TYPE.WAI_BU) {
        // 调用syncCoal接口(外部煤源)
        this.makeSyncCoal(row);
      } else {
        // 调用addToMyCoalSource接口(掌上煤焦或其他)
        this.addToMyCoal({...row, source: COAL_SOURCE_TYPE.WAI_BU});
      }
    },
  },
  props: {
    isOptimization: {
      //  是否是optimization页面
      type: [Boolean],
      default: true,
    },
    // spaceType: { //  类型字段
    //     type: String,
    //     required: true,
    //     default: 'YH',
    //
    // },
    // cokeId: {
    //     type: [String],
    //     required: false
    // },
    formType: {
      type: [String],
      required: false,
      default: "MWASH",
    },
  },
};
</script>
<style lang="scss" scoped>
@import '@/styles/router-page.scss';

.subscript {
  color: #fff;
  position: absolute;
  height: 0;
  width: 0;
  top: 0;
  right: 0;
  border-top: solid 15px #409eff;
  border-right: solid 15px #409eff;
  border-left: solid 15px transparent;
  border-bottom: solid 15px transparent;

  span {
    cursor: default;
    position: absolute;
    top: -20px;
    right: -13px;
    color: #fff;
    z-index: 9;
  }
}

.saveSchemeDialog {
  ::v-deep .el-dialog__body {
    height: auto;
  }
}

.addCoal {
  ::v-deep .el-dialog__body {
    padding: 0 35px;
    height: 85vh;
  }
}

::v-deep .el-select .el-input .el-select__caret {
  color: #f8a20f;
}

.yellow-input ::v-deep .el-input__inner,
.el-input__inner:focus {
  border-radius: 4px;
  border: 1px solid #f8a20f;
  color: #f8a20f;
}

::v-deep .el-select .el-input.is-focus .el-input__inner,
::v-deep .el-select .el-input__inner:focus {
  border: 1px solid #f8a20f;
}

.card-wrap {
  position: absolute;
  margin-top: 15px;
  width: 1.5px;
  height: 80px;
  border-radius: 3px;
  background: #ebebeb;
  right: 31%;
  top: 50%;
  transform: translateY(-50%);
  margin-top: 10px;
}

.res-title {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  font-size: 18px;
}

::v-deep .el-card {
  border: none;
}

::v-deep .el-card.is-always-shadow {
  box-shadow: none;
}

::v-deep .el-collapse {
  border: none;
}

::v-deep .el-collapse-item__wrap {
  border: none;
}

::v-deep .el-tag.el-tag--danger {
  margin-bottom: 5px;
}

::v-deep .el-table .cell .el-input__inner {
  font-size: 12px;
  height: 30px;
  line-height: 30px;
}

::v-deep .el-table__body-wrapper .cell {
  font-size: 12px;
  font-family: unset;
  height: 30px;
  line-height: 30px;
}

::v-deep .el-table .cell .el-input__inner {
  font-size: 12px;
  font-family: unset;
  height: 30px;
  line-height: 30px;
}

.content {
  background: #f7f7f7;
  padding: 0 10px;
  height: 100%;
  overflow-y: auto;
}

.app-layout {
  height: 100%;
}

.single-wrap {
  font-weight: 400 !important;
  text-align: right;
}

.card-title {
  font-size: 14px;
  height: 16px;
  line-height: 16px;
  // font-weight: bold;
  border-left: #33cad9 2px solid;
  padding-left: 4px;
}

.action {
  height: 28px;
  line-height: 28px;
}

.table-content {
  display: flex;
  width: 100%;
  position: relative;

  .table-title {
    width: 120px;
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ebeef5;
    border-right: none;
  }

  .el-table {
    flex: 1;
  }

  .table-action {
    padding: 0 5px;
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 0;
    bottom: 2px;

    button {
      margin-top: 5px;
    }
  }
}

.mb0 {
  .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 0;
  }
}

.textCenter {
  text-align: center;
  vertical-align: top;
  font-size: 12px;
  margin-top: 8px;
}

.tc {
  text-align: center;
  margin-left: -6px;
}
.container {
  width: 100%;
  display: inline-block;

  p {
    font-size: 14px;
    color: #595ec9;
    margin: 0;
    padding-left: 5px;
    font-weight: bold;
    line-height: 10px;
    height: 20px;
  }

  .single {
    display: inline-block;
    height: 28px;
    line-height: 28px;
    width: 100px;
    text-align: center;
    > span {
      display: inline-block;
      margin: 0 5px;
      font-size: 12px;
      font-weight: 600;
      color: #606266;
    }

    > input {
      height: 28px;
      line-height: 28px;
      border: 1px solid rgb(220, 223, 230);
      border-radius: 4px;
      width: 67px;
      box-sizing: border-box;
      padding: 10px;
    }
  }
}

.mr {
  margin-right: 5px !important;
}

.table-title-bg {
  background-color: #f5f7fa;
}

/*.el-autocomplete /deep/ input {*/
/*  width: 260px*/
/*}*/

/*/deep/ .el-table__body tr.current-row > td {*/
/*  background-color: #ffe1e1 !important;*/
/*}*/

.title-text {
  width: 80px;
  text-align: center;
  //text-indent: 2em;
  // transform: scale(0.8);
}

.card {
  margin-bottom: 10px;

  // margin: px 0;
}

.tag {
  display: flex;
  justify-content: flex-end;
  margin: 5px 0;
}

.steps {
  display: flex;
  width: 116%;
  padding: 5px 30px;

  .step-out {
    position: relative;
    flex: 1;
    display: flex;
    // flex-direction: column;
    align-items: center;

    .step {
      /*flex: 1;*/
      width: 30px;
      height: 30px;
    }

    .line {
      // position: absolute;
      // width: calc(100% - 32px);
      /*height: 0;*/
      flex: 1;
      border: 2px solid #f8a20f;
      background-color: #f8a20f;
      border-radius: 2px;
      margin: 0 8px;
      // top: 16px;

      // left: calc(50% + 16px);
    }

    .gray-line {
      border-radius: 2px;
      margin: 0 8px;
      flex: 1;
      background-color: #ccc;
      border: 2px solid #ccc;
    }
  }
}

.gray-step {
  /*flex: 1;*/
  width: 30px;
  height: 30px;
  margin: 5px;
  text-align: center;
  line-height: 30px;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  border-radius: 50%;
  background: #ccc;
}

.checkBg {
  //background: url('~@/assets/images/bg123.jpg') no-repeat;
  background: url('~@/assets/my/checkBg.png');
  background-size: 35px 35px;
  height: 35px;
  width: 35px;
  text-align: center;
  line-height: 35px;
  font-size: 14px;
  // font-weight: bold;
  color: #fff;
  border-radius: 50%;
  text-indent: 0.05rem;
}

.text {
  line-height: 25px;
  font-size: 14px;
  margin-left: 5px;
  color: #f8a20f;
}

// .under-line {
//   color: #33cad9;
//   text-decoration: underline;
//   padding-bottom: 2px;
// }

.black {
  color: #000;
}

.redColor {
  color: red;
}

.coal-table {
  ::v-deep {
    .el-table__cell {
      padding: 1px 0 !important;
    }
  }
}

::v-deep {
  .el-card__body {
    padding: 10px;
  }
}

::v-deep {
  .el-table__footer-wrapper tbody td.el-table__cell,
  .el-table__fixed-footer-wrapper tbody td.el-table__cell {
    //background-color: #fff8f8;
  }

  // 选中 .el-table__footer-wrapper 第一个.cell
  .el-table__footer-wrapper .cell:first-child {
    //height: 35px;
  }

  .el-table .el-table__footer-wrapper .cell,
  .el-table .el-table__fixed-footer-wrapper .cell {
    padding: 0;
    text-align: center;
    //height: 35px;
    .sampleName_cell {
      height: 100%;
      display: flex;
      justify-content: center;
      text-align: center;
      align-content: center;
      flex-direction: row;
      align-items: center;
    }

    .cell {
      flex: 1;
      display: flex;
      flex-direction: column;
      line-height: normal;
      justify-content: space-between;

      div {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border-bottom: #ebeef5 1px solid;
        box-sizing: content-box;

        &:last-of-type {
          border-bottom: none;
        }
      }
    }
  }

  .el-table__footer-wrapper tbody td,
  .el-table__fixed-footer-wrapper tbody td {
    padding: 0;
  }

  .is-active .el-submenu__title {
    color: #2878ff;
  }
}

.noPaddingInput {
  ::v-deep {
    .el-input__inner {
      padding: 0 6px !important;
      text-align: center;
    }
  }
}

/* 固定在屏幕中间的loading样式 */
::v-deep .fixed-center-loading {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;

  .el-loading-mask {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
  }

  .el-loading-spinner {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin-top: 0 !important;
  }

  .el-loading-text {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, calc(-50% + 60px)) !important;
    margin-top: 0 !important;
    font-size: 16px !important;
    font-weight: bold !important;
    color: #000 !important;
    text-align: center !important;
    white-space: nowrap !important;
  }
}
</style>
